# 标准库导入
import csv
import os
import shutil
import subprocess
import time
import tkinter as tk
from io import StringIO
from tkinter import filedialog

# 第三方库导入
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_TABLE_ALIGNMENT
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Pt
from tqdm import tqdm



def get_excel_sheet_names(file_path):
    """
    读取 Excel 文件并返回所有工作表的名称。

    :param file_path: Excel 文件的路径
    :return: 包含所有工作表名称的列表
    """
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
    except Exception as e:
        print(f"读取 Excel 文件时出错: {e}")
    return []


def filter_sheet_names(sheet_names, keyword):
    """
    过滤出包含指定关键字的工作表名称。

    :param sheet_names: 所有工作表名称的列表
    :param keyword: 用于过滤的关键字
    :return: 包含指定关键字的工作表名称列表
    """
    return [name for name in sheet_names if keyword in name]


def read_and_combine_sheets(file_path, sheet_names):
    """
    读取指定工作表的数据并纵向合并。

    :param file_path: Excel 文件的路径
    :param sheet_names: 要读取的工作表名称列表
    :return: 合并后的 DataFrame
    """
    try:
        excel_file = pd.ExcelFile(file_path)
        print("正在读取和合并工作表...")

        # 使用生成器表达式减少内存占用
        df_generator = (excel_file.parse(sheet_name)
                       for sheet_name in tqdm(sheet_names, desc="读取工作表", unit="sheet"))

        # 直接合并，避免创建中间列表
        return pd.concat(df_generator, axis=0, ignore_index=True)
    except Exception as e:
        print(f"读取工作表数据时出错: {e}")
        return pd.DataFrame()


def print_unique_values(df, column_name):
    """
    打印指定列的不重复数据及其数量。

    :param df: 数据 DataFrame
    :param column_name: 指定列名
    """
    unique_values = []
    if column_name in df.columns:
        unique_values = df[column_name].unique()
        # print(f"{column_name} 列中不重复的数据: {unique_values}")
        # print(f"{column_name} 列中不重复数据的数量: {len(unique_values)}")
    else:
        print(f"合并后的 DataFrame 中不存在 {column_name} 列。")
    return unique_values


def calculate_and_add_columns(df):
    """
    计算并添加偏差相关列到 DataFrame。

    :param df: 数据 DataFrame
    :return: 添加列后的 DataFrame
    """
    required_columns = ['Quality Gate X', 'Nominal X', 'Quality Gate Y', 'Nominal Y',
                        'Quality Gate Z', 'Nominal Z', 'Reference X', 'Reference Y', 'Reference Z']

    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"合并后的 DataFrame 中缺少以下列: {missing_columns}，无法计算偏差列。")
        return df

    # 使用向量化操作批量计算所有偏差列
    coordinates = ['X', 'Y', 'Z']

    # 批量计算mapvision dev
    for coord in coordinates:
        df[f'map_dev_{coord}'] = df[f'Quality Gate {coord}'] - df[f'Nominal {coord}']

    # 批量计算cmm dev
    for coord in coordinates:
        df[f'cmm_dev_{coord}'] = df[f'Reference {coord}'] - df[f'Nominal {coord}']

    # 批量计算mapvision - cmm dev
    for coord in coordinates:
        df[f'mapvision_cmm_dev_{coord}'] = df[f'map_dev_{coord}'] - df[f'cmm_dev_{coord}']

    return df


def _setup_axis(ax, df_filter, measure_coordinate, sn_quantity, show_xlabel=True):
    """设置坐标轴的通用配置"""
    if not show_xlabel:
        ax.get_xaxis().set_visible(False)
    else:
        ax.set_xlabel('snplus')
        ax.set_ylabel('deviation')

    ax.tick_params(axis='both', labelsize=6)
    ax.set_xticks(np.arange(0, (sn_quantity + 1), 1))

    # 绘制公共的容差线
    ax.plot(df_filter['snplus'], df_filter[f'Tolerance Upper {measure_coordinate}'],
            color='red', linewidth=1.5, linestyle='--')
    ax.plot(df_filter['snplus'], df_filter[f'Tolerance Lower {measure_coordinate}'],
            color='red', linewidth=1.5, linestyle='--')
    ax.plot(df_filter['snplus'],
            (df_filter[f'Tolerance Upper {measure_coordinate}'] +
             df_filter[f'Tolerance Lower {measure_coordinate}']) / 2,
            color='green', linewidth=1.5)


def _plot_ax1_data(ax1, df_filter, measure_coordinate):
    """绘制第一个子图的数据"""
    ax1.plot(df_filter['snplus'], df_filter[f'map_dev_{measure_coordinate}'],
             color=(0/255, 248/255, 201/255), linewidth=1.5, marker='o', markersize=3)
    ax1.plot(df_filter['snplus'], df_filter[f'cmm_dev_{measure_coordinate}'],
             color='purple', linewidth=1.5, marker='o', markersize=3)


def _plot_ax2_data(ax2, df_filter, measure_coordinate):
    """绘制第二个子图的数据"""
    ax2.plot(df_filter['snplus'], df_filter[f'mapvision_cmm_dev_{measure_coordinate}'],
             color='blue', linewidth=1.5, marker='o', markersize=3)

    # 添加数值标注
    for x, y in zip(df_filter['snplus'], df_filter[f'mapvision_cmm_dev_{measure_coordinate}']):
        ax2.annotate(f'{y:.3f}', xy=(x, y), xytext=(0, 5),
                    textcoords='offset points', fontsize=7, rotation=90, color='blue')


# 图形输出
def fig_output(df, points, cache_dir):
    """生成所有测量点的图形"""
    coordinate_list = ['X', 'Y', 'Z']
    print("正在生成图形...")
    total_combinations = len(points) * len(coordinate_list)

    with tqdm(total=total_combinations, desc="生成图形", unit="图") as pbar:
        for point in points:
            for measure_coordinate in coordinate_list:
                # 按点过滤dataframe行并排序
                df_filter = df[df['Feature Name'] == point].sort_values(by=['snplus'])
                sn_quantity = len(df_filter['sn_map'].drop_duplicates())

                # 创建包含两个垂直子图的图形和坐标轴
                fig, (ax1, ax2) = plt.subplots(nrows=2, ncols=1, figsize=(5.6, 3.4), sharex=True)
                fig.suptitle(f'{point} - {measure_coordinate}', fontsize=10)

                # 设置坐标轴
                _setup_axis(ax1, df_filter, measure_coordinate, sn_quantity, show_xlabel=False)
                _setup_axis(ax2, df_filter, measure_coordinate, sn_quantity, show_xlabel=True)

                # 绘制数据
                _plot_ax1_data(ax1, df_filter, measure_coordinate)
                _plot_ax2_data(ax2, df_filter, measure_coordinate)

                # 保存图形
                fig.savefig(os.path.join(cache_dir, f"{point}_{measure_coordinate}.png"))
                plt.close()
                pbar.update(1)


def _add_image_to_document(document, image_path):
    """向文档添加图片"""
    paragraph = document.add_paragraph()
    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    run = paragraph.add_run("")
    run.add_picture(image_path)


def _create_data_table(document, filtered_df, measure_coordinate, sn_count):
    """创建数据表格"""
    # 提取数据
    map_dev_data = filtered_df[f'map_dev_{measure_coordinate}'].values
    cmm_dev_data = filtered_df[f'cmm_dev_{measure_coordinate}'].values
    mapvision_cmm_dev_data = filtered_df[f'mapvision_cmm_dev_{measure_coordinate}'].values

    # 计算统计值
    std_dev = np.std(mapvision_cmm_dev_data)
    mean_value = np.mean(mapvision_cmm_dev_data)

    # 添加统计信息段落
    paragraph = document.add_paragraph(
        f'{filtered_df["Feature Name"].iloc[0]} - {measure_coordinate} - '
        f'(Stdev: {std_dev:.3f}, Bias: {mean_value:.3f})'
    )
    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

    # 创建表格
    table = document.add_table(rows=3, cols=sn_count + 1, style="Table Grid")

    # 设置表头
    table.cell(0, 0).text = 'Map dev'
    table.cell(1, 0).text = 'CMM dev'
    table.cell(2, 0).text = 'MAP-CMM'

    # 填充数据
    for col_num, (map_val, cmm_val, diff_val) in enumerate(
        zip(map_dev_data, cmm_dev_data, mapvision_cmm_dev_data), start=1
    ):
        table.cell(0, col_num).text = f"{map_val:.3f}"
        table.cell(1, col_num).text = f"{cmm_val:.3f}"
        table.cell(2, col_num).text = f"{diff_val:.3f}"

    return table


def _format_document_tables(document):
    """格式化文档中的所有表格"""
    for table in document.tables:
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        for row in table.rows:
            for cell in row.cells:
                cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
                cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(10)


# 生成报告
def report(df, points, cache_dir, report_dir, product, time_now, sn_count):
    """生成docx报告"""
    coordinate_list = ['X', 'Y', 'Z']
    document = Document()
    print("正在生成报告...")
    total_combinations = len(points) * len(coordinate_list)

    with tqdm(total=total_combinations, desc="生成报告", unit="页") as pbar:
        for point in points:
            filtered_df = df[df['Feature Name'] == point]
            for measure_coordinate in coordinate_list:
                # 添加图片
                image_path = os.path.join(cache_dir, f"{point}_{measure_coordinate}.png")
                _add_image_to_document(document, image_path)

                # 创建数据表格
                _create_data_table(document, filtered_df, measure_coordinate, sn_count)

                pbar.update(1)

    # 格式化所有表格
    _format_document_tables(document)

    # 保存并打开文档
    save_path = os.path.join(report_dir, f"{product}-{time_now}.docx")
    document.save(save_path)
    subprocess.Popen(['start', save_path], shell=True)



def select_csv_file():
    """选择CSV文件"""
    try:
        root = tk.Tk()
        root.withdraw()
        correlation_csv = filedialog.askopenfilename(
            title="请选择CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if not correlation_csv:
            print("未选择CSV文件，程序退出。")
            exit()

        # 验证文件是否存在
        if not os.path.exists(correlation_csv):
            raise FileNotFoundError(f"选择的文件不存在: {correlation_csv}")

        print(f"已选择文件: {correlation_csv}")
        return correlation_csv
    except Exception as e:
        print(f"选择文件时发生错误: {e}")
        exit()


def setup_directories(product, time_now):
    """设置输出目录"""
    if not os.path.exists('report'):
        os.makedirs('report')

    report_dir = os.path.join('report', f'{product}-{time_now}')
    cache_dir = os.path.join(report_dir, 'cache')
    os.makedirs(report_dir, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)

    return report_dir, cache_dir


def main():
    """主函数"""
    try:
        print("开始执行数据处理程序...")

        # 定义当前时间字符串
        time_now = time.strftime("%Y%m%d-%H%M%S")

        # 选择CSV文件
        correlation_csv = select_csv_file()
        product = os.path.splitext(os.path.basename(correlation_csv))[0]
        print(f"产品名称: {product}")

        # 设置目录
        report_dir, cache_dir = setup_directories(product, time_now)
        print(f"输出目录: {report_dir}")

        # 解析CSV数据
        combined_df = parse_csv_data(correlation_csv)
        if combined_df.empty:
            print("错误: 没有解析到有效数据，程序退出")
            return

        # 处理数据
        combined_df = process_data(combined_df, report_dir, product, time_now)
        if combined_df.empty:
            print("错误: 数据处理后为空，程序退出")
            return

        # 检查必要的列是否存在
        required_columns = ['sn_map', 'Feature Name']
        missing_columns = [col for col in required_columns if col not in combined_df.columns]
        if missing_columns:
            print(f"错误: 缺少必要的列: {missing_columns}")
            return

        # 生成图形和报告
        sn_values = combined_df['sn_map'].unique()
        points = combined_df['Feature Name'].unique()

        if len(points) == 0:
            print("警告: 没有找到有效的测量点")
            return

        print(f"开始生成 {len(points)} 个测量点的图形和报告...")
        fig_output(combined_df, points, cache_dir)
        report(combined_df, points, cache_dir, report_dir, product, time_now, len(sn_values))

        # 清理缓存
        cleanup_cache(cache_dir)

        print("\n" + "=" * 60)
        print("处理完成！")
        print(f"总共处理了 {len(points)} 个测量点")
        print(f"生成了 {len(points) * 3} 个图形")
        print(f"报告已保存到: {report_dir}")
        print("=" * 60)

    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        print("程序异常退出")


def parse_csv_data(correlation_csv):
    """解析CSV文件数据"""
    try:
        print(f"正在解析CSV文件: {correlation_csv}")
        all_data_between = []
        current_block = []
        inside_block = False

        with open(correlation_csv, encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and row[0].strip() == "Raw data":
                    inside_block = True
                    current_block = []
                    continue
                if row and row[0].strip() == "Distance measurements":
                    if inside_block:
                        all_data_between.append(current_block)
                        inside_block = False
                    continue
                if inside_block:
                    current_block.append(row)

        print(f"找到 {len(all_data_between)} 个数据块")

        dfs_list = []
        successful_blocks = 0

        for i, block in enumerate(all_data_between):
            if len(block) < 4:
                print(f"跳过异常区块 {i+1}: 数据行数不足")
                continue

            # 跳过前4行
            data_rows = block[4:]
            if not data_rows:
                print(f"跳过空数据区块 {i+1}")
                continue

            # 转为csv字符串
            csv_str = "\n".join([",".join(row) for row in data_rows])
            try:
                df = pd.read_csv(StringIO(csv_str))
                # 获取sn_map和Reference name行的第二个数据
                sn_map = block[0][1] if len(block[0]) > 1 else None
                sn_cmm = block[2][1] if len(block[2]) > 1 else None
                df['sn_map'] = sn_map
                df['sn_cmm'] = sn_cmm
                dfs_list.append(df)
                successful_blocks += 1
            except Exception as e:
                print(f"区块 {i+1} 读取失败: {e}")
                continue

        print(f"成功解析 {successful_blocks} 个数据块")

        # 读取并合并数据
        if dfs_list:
            combined_df = pd.concat(dfs_list, ignore_index=True)
            print(f"合并后的数据包含 {len(combined_df)} 行")
            return combined_df
        else:
            print("警告: 没有成功解析任何数据块")
            return pd.DataFrame()

    except Exception as e:
        print(f"解析CSV文件时发生错误: {e}")
        return pd.DataFrame()


def process_data(combined_df, report_dir, product, time_now):
    """处理数据：计算偏差、处理序列号、过滤数据"""
    # 设置 Pandas 选项以显示全量数据
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', None)

    # 计算并添加偏差相关列
    print("正在计算偏差相关列...")
    combined_df = calculate_and_add_columns(combined_df)
    print("偏差计算完成")

    # 创建snplus
    print("正在处理序列号...")
    sn_values = print_unique_values(combined_df, 'sn_map')
    print(f"发现 {len(sn_values)} 个唯一序列号")

    for i, sn in enumerate(tqdm(sn_values, desc="处理序列号", unit="个"), 1):
        combined_df.loc[combined_df['sn_map'] == sn, 'snplus'] = i

    # 原始数据Point点
    points_0 = print_unique_values(combined_df, 'Feature Name')
    print(f"原始数据中有 {len(points_0)} 个测量点")

    # 过滤掉 Reference X, Reference Y, Reference Z 列为空的数据
    print("正在过滤缺失CMM数据的点...")
    combined_df = combined_df.dropna(subset=['Reference X', 'Reference Y', 'Reference Z'])

    # 打印 Feature Name 列不重复数据
    points = print_unique_values(combined_df, 'Feature Name')
    print(f"过滤后有 {len(points)} 个有效测量点")

    # 计算两个列表的差值
    points_0_set = set(points_0)
    points_set = set(points)
    diff_points = list(points_0_set - points_set)

    # 将 diff_points 输出到 txt 文件
    print("正在保存缺失CMM数据的点列表...")
    diff_file_path = os.path.join(report_dir, f'miss_cmm_value-{product}-{time_now}.txt')
    with open(diff_file_path, 'w', encoding='utf-8') as f:
        for diff_point in diff_points:
            f.write(f"{diff_point}\n")
    print(f"缺失CMM数据的点: {len(diff_points)} 个")

    print("正在保存处理后的数据...")
    combined_df.to_excel(os.path.join(report_dir, f'{product}-{time_now}.xlsx'))
    print("数据保存完成")

    return combined_df


def cleanup_cache(cache_dir):
    """清理缓存文件夹"""
    # 恢复默认设置
    pd.reset_option('all')
    print("程序执行完成！")

    # 删除 cache_dir 文件夹，删除前检查是否存在
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print(f"已删除缓存文件夹: {cache_dir}")
    else:
        print(f"缓存文件夹不存在，无需删除")


if __name__ == "__main__":
    main()
