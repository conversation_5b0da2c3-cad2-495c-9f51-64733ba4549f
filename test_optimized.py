#!/usr/bin/env python3
"""
测试优化后的main.py代码
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有导入是否正常"""
    try:
        import main
        print("✓ 所有导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_function_definitions():
    """测试所有函数是否正确定义"""
    import main
    
    expected_functions = [
        'get_excel_sheet_names',
        'filter_sheet_names', 
        'read_and_combine_sheets',
        'print_unique_values',
        'calculate_and_add_columns',
        'fig_output',
        'report',
        'select_csv_file',
        'setup_directories',
        'parse_csv_data',
        'process_data',
        'cleanup_cache',
        'main'
    ]
    
    missing_functions = []
    for func_name in expected_functions:
        if not hasattr(main, func_name):
            missing_functions.append(func_name)
    
    if missing_functions:
        print(f"✗ 缺少函数: {missing_functions}")
        return False
    else:
        print("✓ 所有函数定义正确")
        return True

def test_helper_functions():
    """测试辅助函数"""
    import main
    
    # 测试filter_sheet_names
    sheet_names = ['Sheet1', 'Data_Sheet', 'Summary', 'Data_Analysis']
    filtered = main.filter_sheet_names(sheet_names, 'Data')
    expected = ['Data_Sheet', 'Data_Analysis']
    
    if filtered == expected:
        print("✓ filter_sheet_names 函数工作正常")
        return True
    else:
        print(f"✗ filter_sheet_names 函数测试失败: 期望 {expected}, 得到 {filtered}")
        return False

if __name__ == "__main__":
    print("开始测试优化后的代码...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_function_definitions,
        test_helper_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！代码优化成功。")
    else:
        print("✗ 部分测试失败，需要检查代码。")
